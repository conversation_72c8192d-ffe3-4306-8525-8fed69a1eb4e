import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { JwtPayload, UserPayload } from '../types/express';
import { Admin } from '../models/Admin';
import { User } from '../models/User';

export const auth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as JwtPayload;

    // Check if the token belongs to an admin
    if (decoded.role === 'admin' || decoded.isMainAdmin) {
      const admin = await Admin.findById(decoded.id);

      if (!admin) {
        return res.status(401).json({ message: 'Admin not found' });
      }

      req.user = {
        id: decoded.id,
        email: decoded.email || admin.email,
        role: decoded.role,
        isMainAdmin: decoded.isMainAdmin || false
      };
    } else {
      // Check if the token belongs to a regular user
      const user = await User.findById(decoded.id);

      if (!user) {
        return res.status(401).json({ message: 'User not found' });
      }

      req.user = {
        id: decoded.id,
        email: decoded.email || user.email,
        role: decoded.role,
        isMainAdmin: false
      };
    }

    next();
  } catch (error) {
    res.status(401).json({ message: 'Token is not valid' });
  }
};

export const adminAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await auth(req, res, () => {
      // Check if the user is an admin (either regular admin or main admin)
      if (!req.user || (req.user.role !== 'admin' && !req.user.isMainAdmin)) {
        console.log('Access denied for non-admin user:', req.user);
        return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
      }
      next();
    });
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ message: 'Please authenticate' });
  }
};

// Middleware to check if user is a main admin
export const mainAdminAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await auth(req, res, () => {
      // Check if the user is a main admin
      if (!req.user || !req.user.isMainAdmin) {
        console.log('Access denied for non-main admin:', req.user);
        return res.status(403).json({ message: 'Access denied. Main admin privileges required.' });
      }
      next();
    });
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ message: 'Please authenticate' });
  }
};
